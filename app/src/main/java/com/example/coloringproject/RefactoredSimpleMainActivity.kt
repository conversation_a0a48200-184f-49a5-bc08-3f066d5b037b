package com.example.coloringproject

import android.graphics.Bitmap
import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.coloringproject.data.ColorPalette
import com.example.coloringproject.data.ColoringData
import com.example.coloringproject.databinding.ActivitySimpleMainBinding
import com.example.coloringproject.manager.AutoDemoManager
import com.example.coloringproject.manager.ColoringStateManager
import com.example.coloringproject.manager.ProgressSaveManager
import com.example.coloringproject.manager.ProjectLoadManager
import com.example.coloringproject.manager.UIStateManager
import com.example.coloringproject.utils.ImageSaver
import com.example.coloringproject.utils.PermissionHelper
import com.example.coloringproject.utils.PerformanceMonitor
import com.example.coloringproject.utils.PerformanceConfig
import com.example.coloringproject.viewmodel.ColoringViewModel
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 重构后的SimpleMainActivity
 * 通过管理器模式将原来的3189行代码拆分为多个专门的管理器
 */
class RefactoredSimpleMainActivity : AppCompatActivity() {

    private val TAG = "RefactoredSimpleMainActivity"

    // ViewBinding
    private lateinit var binding: ActivitySimpleMainBinding

    // ViewModel
    private val viewModel: ColoringViewModel by viewModels()

    // 管理器
    private lateinit var projectLoadManager: ProjectLoadManager
    private lateinit var coloringStateManager: ColoringStateManager
    private lateinit var progressSaveManager: ProgressSaveManager
    private lateinit var autoDemoManager: AutoDemoManager
    private lateinit var uiStateManager: UIStateManager
    
    // 项目来源信息
    private var currentProjectSource: String? = null
    private var currentResourceSource: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 获取项目ID用于端到端监控
        val projectId = intent.getStringExtra("project_id") ?: "unknown"
        
        // 记录Activity创建开始时间
        PerformanceMonitor.measurePhase("EndToEnd_$projectId", "Activity_onCreate_start") { }
        Log.d("PERFORMANCE_E2E", "RefactoredSimpleMainActivity.onCreate 开始: ${System.currentTimeMillis()}")
        
        // 检测设备性能并配置优化级别
        val performanceLevel = PerformanceConfig.detectPerformanceLevel(this)
        Log.d(TAG, PerformanceConfig.generateConfigReport())
        
        // 根据性能配置决定是否启用监控
        if (PerformanceConfig.enablePerformanceMonitoring()) {
            PerformanceMonitor.startTiming("Activity_onCreate")
            PerformanceMonitor.logMemoryUsage("onCreate_start")
        }
        
        // 记录布局加载时间
        PerformanceMonitor.measurePhase("EndToEnd_$projectId", "Layout_Inflation") {
            binding = ActivitySimpleMainBinding.inflate(layoutInflater)
            setContentView(binding.root)
        }
        
        Log.d(TAG, "onCreate started")
        
        // 初始化管理器
        if (PerformanceConfig.enablePerformanceMonitoring()) {
            PerformanceMonitor.measurePhase("Activity_Init", "Managers") {
                initializeManagers()
            }
        } else {
            initializeManagers()
        }
        
        // 设置共享元素过渡（根据性能配置决定）
        if (PerformanceConfig.enableSharedElementTransition()) {
            if (PerformanceConfig.enablePerformanceMonitoring()) {
                PerformanceMonitor.measurePhase("Activity_Init", "SharedTransition") {
                    setupSharedElementTransition()
                }
            } else {
                setupSharedElementTransition()
            }
        }
        
        // 初始化UI
        if (PerformanceConfig.enablePerformanceMonitoring()) {
            PerformanceMonitor.measurePhase("Activity_Init", "UI") {
                uiStateManager.initializeUI()
            }
        } else {
            uiStateManager.initializeUI()
        }
        
        // 设置ColoringView事件
        if (PerformanceConfig.enablePerformanceMonitoring()) {
            PerformanceMonitor.measurePhase("Activity_Init", "Events") {
                setupColoringViewEvents()
            }
        } else {
            setupColoringViewEvents()
        }
        
        // 处理Intent参数并加载项目
        if (PerformanceConfig.enablePerformanceMonitoring()) {
            PerformanceMonitor.measurePhase("Activity_Init", "ProjectLoad") {
                handleIntentAndLoadProject()
            }
        } else {
            handleIntentAndLoadProject()
        }
        
        if (PerformanceConfig.enablePerformanceMonitoring()) {
            PerformanceMonitor.endTiming("Activity_onCreate")
            PerformanceMonitor.logMemoryUsage("onCreate_end")
            Log.d(TAG, PerformanceMonitor.generateReport("Activity_Init"))
        }
        
        Log.d(TAG, "onCreate completed")
    }

    /**
     * 初始化所有管理器
     */
    private fun initializeManagers() {
        projectLoadManager = ProjectLoadManager(this)
        coloringStateManager = ColoringStateManager()
        progressSaveManager = ProgressSaveManager(this)
        autoDemoManager = AutoDemoManager()
        uiStateManager = UIStateManager(this, binding)
        
        // 设置管理器之间的回调
        setupManagerCallbacks()
    }

    /**
     * 设置管理器之间的回调关系
     */
    private fun setupManagerCallbacks() {
        // 填色状态管理器回调
        coloringStateManager.setStateChangeListener(object : ColoringStateManager.StateChangeListener {
            override fun onColorSelected(color: ColorPalette) {
                Log.d(TAG, "收到颜色选择回调: ${color.name}")
                val colorIndex = coloringStateManager.getProcessedPalette().indexOfFirst { 
                    normalizeColorHex(it.colorHex) == normalizeColorHex(color.colorHex)
                }
                Log.d(TAG, "找到颜色索引: $colorIndex")
                if (colorIndex >= 0) {
                    uiStateManager.selectColor(color, colorIndex)
                } else {
                    Log.w(TAG, "未找到颜色索引: ${color.colorHex}")
                }
            }

            override fun onRegionFilled(regionId: Int) {
                binding.coloringView.addFilledRegion(regionId)
                
                // 异步保存进度
                val coloringData = coloringStateManager.currentColoringData
                if (coloringData != null) {
                    progressSaveManager.saveProgressFast(
                        getStandardizedProjectName(),
                        coloringData,
                        coloringStateManager.filledRegions,
                        currentProjectSource,
                        currentResourceSource
                    )
                    
                    // 调度预览图生成
                    progressSaveManager.schedulePreviewImageGeneration(
                        getStandardizedProjectName(),
                        coloringData,
                        coloringStateManager.filledRegions
                    ) {
                        binding.coloringView.captureArtwork(false)
                    }
                }
            }

            override fun onProgressUpdated(filled: Int, total: Int) {
                uiStateManager.updateTotalProgress(filled, total)
                
                // 更新当前颜色进度
                val currentColor = coloringStateManager.currentSelectedColor
                if (currentColor != null) {
                    val progress = coloringStateManager.getCurrentColorProgress()
                    uiStateManager.updateCurrentColorDisplay(currentColor, progress)
                    
                    val colorIndex = coloringStateManager.getProcessedPalette().indexOfFirst { 
                        it.colorHex == currentColor.colorHex 
                    }
                    if (colorIndex >= 0) {
                        val progressPercentage = if (progress.second > 0) {
                            (progress.first * 100) / progress.second
                        } else 0
                        uiStateManager.updateColorProgress(colorIndex, progressPercentage)
                    }
                }
            }

            override fun onColorCompleted(colorHex: String, regionCount: Int) {
                // 颜色完成时更新颜色列表（移除已完成的颜色）并切换到下一个未完成的颜色
                updateColorListAfterCompletion()
                switchToNextUnfinishedColor()
            }

            override fun onProjectCompleted() {
                uiStateManager.showCompletionDialog {
                    saveArtworkToGallery()
                }
            }
        })

        // UI状态管理器回调
        uiStateManager.setUIInteractionListener(object : UIStateManager.UIInteractionListener {
            override fun onColorSelected(color: ColorPalette) {
                coloringStateManager.selectColor(color)
            }

            override fun onHintUsed() {
                uiStateManager.useHint()
            }

            override fun onZoomIn() {
                binding.coloringView.zoomIn()
            }

            override fun onZoomOut() {
                binding.coloringView.zoomOut()
            }

            override fun onZoomFit() {
                binding.coloringView.zoomToFit()
            }

            override fun onBackPressed() {
                handleBackPressed()
            }

            override fun onSaveImage() {
                saveArtworkToGallery()
            }

            override fun onResetProject() {
                resetProject()
            }

            override fun onProjectSelectionRequested() {
                showProjectSelectionMenu()
            }
        })

        // 自动演示管理器回调
        autoDemoManager.setAutoDemoListener(object : AutoDemoManager.AutoDemoListener {
            override fun onColorSelected(color: ColorPalette) {
                coloringStateManager.selectColor(color)
            }

            override fun onRegionFilled(regionId: Int) {
                coloringStateManager.fillRegion(regionId)
            }

            override fun onDemoCompleted() {
                uiStateManager.showCompletionDialog {
                    saveArtworkToGallery()
                }
            }

            override fun onProgressUpdate(current: Int, total: Int) {
                // 演示进度更新可以在这里处理
            }
        })
    }

    /**
     * 设置ColoringView事件
     */
    private fun setupColoringViewEvents() {
        binding.coloringView.onRegionTouched = { result ->
            if (result.isValidTouch && result.regionId != null) {
                coloringStateManager.fillRegion(result.regionId)
            }
        }

        binding.coloringView.onProjectCompleted = {
            // 项目完成处理已在StateChangeListener中处理
        }

        binding.coloringView.onColorCompleted = { colorHex, regionCount ->
            // 颜色完成处理已在StateChangeListener中处理
        }

        binding.coloringView.onColorAutoSelected = { colorHex ->
            val success = coloringStateManager.autoSelectColorByHex(colorHex)
            if (success) {
                val currentColor = coloringStateManager.currentSelectedColor
                if (currentColor != null) {
                    uiStateManager.showToast("已选择颜色: ${currentColor.name}")
                }
            }
        }
    }

    /**
     * 处理Intent参数并加载项目 - 统一的智能加载逻辑
     */
    private fun handleIntentAndLoadProject() {
        val projectId = intent.getStringExtra("project_id")
        val projectName = intent.getStringExtra("project_name")
        val projectSource = intent.getStringExtra("project_source")

        // 保存项目来源信息
        currentProjectSource = projectSource
        currentResourceSource = projectSource // 暂时使用相同的值，后续可以细化

        Log.d(TAG, "=== 统一项目加载逻辑 ===")
        Log.d(TAG, "projectId: $projectId")
        Log.d(TAG, "projectName: $projectName")
        Log.d(TAG, "projectSource: $projectSource")
        Log.d(TAG, "Intent extras: ${intent.extras}")
        Log.d(TAG, "========================")

        when {
            projectId != null -> {
                Log.d(TAG, "使用projectId智能加载: $projectId")
                loadProjectWithSmartProgressDetection(projectId, projectName, projectSource)
            }
            projectName != null -> {
                Log.d(TAG, "使用projectName智能加载: $projectName")
                loadProjectWithSmartProgressDetection(projectName, projectName, projectSource)
            }
            else -> {
                Log.d(TAG, "加载默认项目")
                loadFirstValidatedProject()
            }
        }
    }

    /**
     * 统一的智能项目加载方法
     * 确保项目名称在整个流程中保持一致
     */
    private fun loadProjectWithSmartProgressDetection(projectId: String, projectName: String?, projectSource: String?) {
        lifecycleScope.launch {
            // 记录项目加载开始
            PerformanceMonitor.measurePhase("EndToEnd_$projectId", "ShowLoading") {
                uiStateManager.showLoading()
            }
            
            try {
                // 简化逻辑：统一使用项目ID，不考虑兼容性
                val unifiedProjectName = projectId
                
                Log.d("PERFORMANCE_E2E", "=== 开始项目加载流程 ===")
                Log.d("PERFORMANCE_E2E", "项目ID: $projectId")
                Log.d("PERFORMANCE_E2E", "加载开始时间: ${System.currentTimeMillis()}")
                
                // 使用统一名称检测进度
                val progressResult = PerformanceMonitor.measurePhase("EndToEnd_$projectId", "LoadSavedProgress") {
                    progressSaveManager.loadSavedProgress(unifiedProjectName)
                }
                val hasProgress = progressResult is com.example.coloringproject.utils.LoadResult.Success
                
                Log.d(TAG, "进度检测详细信息:")
                Log.d(TAG, "  - 使用名称: $unifiedProjectName")
                Log.d(TAG, "  - 检测结果: hasProgress=$hasProgress")
                Log.d(TAG, "  - 结果类型: ${progressResult::class.simpleName}")
                if (progressResult is com.example.coloringproject.utils.LoadResult.Success) {
                    Log.d(TAG, "  - 已填色区域数量: ${progressResult.data.filledRegions.size}")
                    Log.d(TAG, "  - 填色区域ID: ${progressResult.data.filledRegions}")
                } else if (progressResult is com.example.coloringproject.utils.LoadResult.Error) {
                    Log.d(TAG, "  - 错误信息: ${progressResult.message}")
                }
                
                // 使用统一名称加载项目文件
                val projectResult = PerformanceMonitor.measurePhase("EndToEnd_$projectId", "LoadProjectFile") {
                    projectLoadManager.loadProject(unifiedProjectName, projectSource)
                }
                
                when (projectResult) {
                    is ProjectLoadManager.LoadResult.Success -> {
                        Log.d("PERFORMANCE_E2E", "项目文件加载完成，开始设置项目")
                        
                        if (hasProgress) {
                            // 有进度：使用快速设置（使用最新的项目文件数据，只恢复填色区域）
                            val fullProgressData = (progressResult as com.example.coloringproject.utils.LoadResult.Success).data
                            Log.d("PERFORMANCE_E2E", "使用快速设置恢复进度: ${fullProgressData.filledRegions.size}个区域")
                            
                            PerformanceMonitor.measurePhase("EndToEnd_$projectId", "SetupProjectFast") {
                                setupProjectFast(projectResult.coloringData, projectResult.outlineBitmap, fullProgressData.filledRegions)
                            }
                        } else {
                            // 无进度：使用标准设置
                            Log.d("PERFORMANCE_E2E", "使用标准设置（无进度）")
                            
                            PerformanceMonitor.measurePhase("EndToEnd_$projectId", "SetupProject") {
                                setupProject(projectResult.coloringData, projectResult.outlineBitmap)
                            }
                        }
                    }
                    is ProjectLoadManager.LoadResult.Error -> {
                        Log.e(TAG, "项目加载失败: $unifiedProjectName, 错误: ${projectResult.message}")
                        uiStateManager.showProjectLoadError("加载失败", "无法找到项目: $unifiedProjectName\n请确保assets文件夹中存在 $unifiedProjectName.json 和 $unifiedProjectName.png") {
                            loadProjectWithSmartProgressDetection(projectId, projectName, projectSource)
                        }
                    }
                    is ProjectLoadManager.LoadResult.RequiresDownload -> {
                        uiStateManager.showToast("项目需要下载，功能开发中...")
                        loadFirstValidatedProject()
                    }
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "智能加载失败: $projectId", e)
                uiStateManager.showError("加载项目失败: ${e.message}")
            }
        }
    }

    /**
     * 根据项目ID加载项目（保留用于兼容性）
     */
    private fun loadProjectById(projectId: String, projectSource: String?) {
        loadProjectWithSmartProgressDetection(projectId, null, projectSource)
    }

    /**
     * 从Gallery加载项目（保留用于兼容性）
     */
    private fun loadProjectFromGallery(projectName: String, hasProgress: Boolean) {
        Log.d(TAG, "Gallery加载重定向到智能加载: $projectName")
        loadProjectWithSmartProgressDetection(projectName, projectName, "BUILT_IN")
    }

    /**
     * 根据项目名称加载项目（保留用于兼容性）
     */
    private fun loadProjectByName(projectName: String) {
        Log.d(TAG, "项目名称加载重定向到智能加载: $projectName")
        loadProjectWithSmartProgressDetection(projectName, projectName, "BUILT_IN")
    }

    /**
     * 加载第一个验证过的项目
     */
    private fun loadFirstValidatedProject() {
        lifecycleScope.launch {
            try {
                val projectsResult = projectLoadManager.getValidatedProjects()
                if (projectsResult.isSuccess) {
                    val projects = projectsResult.getOrNull()!!
                    val validProjects = projects.filter { it.isValid }
                    
                    if (validProjects.isNotEmpty()) {
                        val firstProject = validProjects.first()
                        Log.d(TAG, "加载第一个有效项目: ${firstProject.name}")
                        
                        // 使用统一的智能加载逻辑
                        loadProjectWithSmartProgressDetection(firstProject.name, firstProject.name, "BUILT_IN")
                        
                        // 如果有多个项目，显示选择器
                        if (validProjects.size > 1) {
                            uiStateManager.showProjectSelector(validProjects) { selectedProject ->
                                loadProjectByName(selectedProject.name)
                            }
                        }
                    } else {
                        uiStateManager.showError("没有找到有效的填色项目")
                    }
                } else {
                    uiStateManager.showError("项目验证失败")
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载默认项目失败", e)
                uiStateManager.showError("加载默认项目失败: ${e.message}")
            }
        }
    }

    /**
     * 设置项目 - 优化版本：异步初始化
     */
    private fun setupProject(coloringData: ColoringData, outlineBitmap: Bitmap) {
        Log.d(TAG, "=== 开始异步项目设置 ===")
        
        lifecycleScope.launch {
            try {
                // 第一阶段：快速显示基础UI
                setupProjectPhase1()
                
                // 第二阶段：后台处理数据
                val processedData = setupProjectPhase2(coloringData, outlineBitmap)
                
                // 第三阶段：应用数据到UI
                setupProjectPhase3(processedData)
                
                Log.d(TAG, "=== 异步项目设置完成 ===")
            } catch (e: Exception) {
                Log.e(TAG, "异步项目设置失败", e)
                uiStateManager.showError("项目设置失败: ${e.message}")
            }
        }
    }
    
    /**
     * 第一阶段：快速显示基础UI
     */
    private fun setupProjectPhase1() {
        Log.d(TAG, "Phase 1: 快速UI初始化")
        
        // 显示加载状态
        uiStateManager.showLoading()
        
        // 更新项目基础信息（不依赖复杂计算）
        uiStateManager.updateProjectInfo(getStandardizedProjectName(), 0)
    }
    
    /**
     * 第二阶段：后台处理数据
     */
    private suspend fun setupProjectPhase2(coloringData: ColoringData, outlineBitmap: Bitmap): ProjectSetupData = withContext(Dispatchers.IO) {
        Log.d(TAG, "Phase 2: 后台数据处理")
        PerformanceMonitor.logMemoryUsage("Phase2_start")
        
        // 初始化填色状态管理器
        PerformanceMonitor.measurePhase("Project_Setup", "StateManager_Init") {
            coloringStateManager.initializeProject(coloringData)
        }
        
        // 获取处理后的数据
        val processedPalette = PerformanceMonitor.measurePhase("Project_Setup", "ProcessPalette") {
            coloringStateManager.getProcessedPalette()
        }
        val colorInfoList = PerformanceMonitor.measurePhase("Project_Setup", "ColorInfoList") {
            coloringStateManager.getColorInfoList()
        }
        
        PerformanceMonitor.logMemoryUsage("Phase2_end")
        
        ProjectSetupData(
            coloringData = coloringData,
            outlineBitmap = outlineBitmap,
            processedPalette = processedPalette,
            colorInfoList = colorInfoList
        )
    }
    
    /**
     * 第三阶段：应用数据到UI
     */
    private suspend fun setupProjectPhase3(data: ProjectSetupData) = withContext(Dispatchers.Main) {
        Log.d(TAG, "Phase 3: 应用数据到UI")
        PerformanceMonitor.logMemoryUsage("Phase3_start")
        
        // 更新UI组件
        PerformanceMonitor.measurePhase("Project_Setup", "UI_UpdateColorList") {
            uiStateManager.updateColorList(data.colorInfoList)
        }
        PerformanceMonitor.measurePhase("Project_Setup", "UI_UpdateProjectInfo") {
            uiStateManager.updateProjectInfo(getStandardizedProjectName(), data.coloringData.metadata.totalRegions)
        }
        
        // 确保颜色设置在ColoringView完全初始化之后进行
        if (data.processedPalette.isNotEmpty()) {
            val firstColor = data.processedPalette.first()
            
            // 设置数据完成回调，确保颜色设置在数据准备好之后进行
            binding.coloringView.onDataSetupComplete = {
                PerformanceMonitor.measurePhase("Project_Setup", "SetDefaultColor") {
                    binding.coloringView.setCurrentColor(firstColor.colorHex)
                    coloringStateManager.selectColor(firstColor)
                    Log.d(TAG, "新项目默认颜色设置完成: ${firstColor.name}")
                }
                // 清除回调，避免重复调用
                binding.coloringView.onDataSetupComplete = null
            }
            
            Log.d(TAG, "准备设置默认颜色: ${firstColor.name}")
        }
        
        // 设置ColoringView数据（优化版本）
        PerformanceMonitor.measurePhase("Project_Setup", "ColoringView_SetData") {
            binding.coloringView.setColoringDataAsync(data.coloringData, data.outlineBitmap)
        }
        
        uiStateManager.showReady()
        PerformanceMonitor.logMemoryUsage("Phase3_end")
        
        // 结束端到端性能监控
        val projectId = coloringStateManager.currentColoringData?.let { 
            intent.getStringExtra("project_id") ?: "unknown" 
        } ?: "unknown"
        
        PerformanceMonitor.measurePhase("EndToEnd_$projectId", "ShowReady_Complete") { }
        
        // 计算并记录端到端总时间
        val endToEndTime = PerformanceMonitor.endTiming("EndToEnd_ProjectClick_$projectId")
        Log.i("PERFORMANCE_E2E", "=== 端到端性能监控完成 ===")
        Log.i("PERFORMANCE_E2E", "项目ID: $projectId")
        Log.i("PERFORMANCE_E2E", "总耗时: ${endToEndTime}ms")
        Log.i("PERFORMANCE_E2E", "完成时间: ${System.currentTimeMillis()}")
        
        // 输出详细的阶段性能报告
        Log.i("PERFORMANCE_E2E", PerformanceMonitor.generateReport("EndToEnd_$projectId"))
        Log.i(TAG, PerformanceMonitor.generateReport("Project_Setup"))
    }
    
    /**
     * 项目设置数据类
     */
    private data class ProjectSetupData(
        val coloringData: ColoringData,
        val outlineBitmap: Bitmap,
        val processedPalette: List<ColorPalette>,
        val colorInfoList: List<com.example.coloringproject.model.ColorInfo>
    )

    /**
     * 快速设置项目（用于进度恢复）- 优化版本：异步处理
     */
    private fun setupProjectFast(coloringData: ColoringData, outlineBitmap: Bitmap, savedFilledRegions: Set<Int>) {
        Log.d(TAG, "=== 开始异步快速设置项目 ===")
        Log.d(TAG, "恢复填色区域数量: ${savedFilledRegions.size}")
        
        lifecycleScope.launch {
            try {
                // 第一阶段：快速显示基础UI和进度
                setupProjectFastPhase1(savedFilledRegions.size, coloringData.metadata.totalRegions)
                
                // 第二阶段：后台处理数据和验证
                val processedData = setupProjectFastPhase2(coloringData, outlineBitmap, savedFilledRegions)
                
                // 第三阶段：应用数据和恢复进度
                setupProjectFastPhase3(processedData)
                
                Log.d(TAG, "=== 异步快速设置完成 ===")
            } catch (e: Exception) {
                Log.e(TAG, "异步快速设置失败", e)
                uiStateManager.showError("进度恢复失败: ${e.message}")
            }
        }
    }
    
    /**
     * 快速设置第一阶段：立即显示进度信息
     */
    private fun setupProjectFastPhase1(filledCount: Int, totalRegions: Int) {
        Log.d(TAG, "Fast Phase 1: 立即显示进度")
        
        // 立即显示加载状态和基础进度
        uiStateManager.showLoading()
        uiStateManager.updateProjectInfo(getStandardizedProjectName(), totalRegions)
        uiStateManager.updateTotalProgress(filledCount, totalRegions)
    }
    
    /**
     * 快速设置第二阶段：后台数据处理
     */
    private suspend fun setupProjectFastPhase2(
        coloringData: ColoringData, 
        outlineBitmap: Bitmap, 
        savedFilledRegions: Set<Int>
    ): FastProjectSetupData = withContext(Dispatchers.IO) {
        Log.d(TAG, "Fast Phase 2: 后台数据处理和验证")
        
        // 快速初始化状态管理器（包含区域验证）
        coloringStateManager.initializeProjectFast(coloringData, savedFilledRegions)
        
        // 获取验证后的数据
        val validFilledRegions = coloringStateManager.filledRegions.toSet()
        val processedPalette = coloringStateManager.getProcessedPalette()
        val colorInfoList = coloringStateManager.getColorInfoList()
        val currentColor = coloringStateManager.currentSelectedColor
        val totalProgress = coloringStateManager.getTotalProgress()
        
        Log.d(TAG, "数据验证完成: ${validFilledRegions.size}/${savedFilledRegions.size} 区域有效")
        
        FastProjectSetupData(
            coloringData = coloringData,
            outlineBitmap = outlineBitmap,
            validFilledRegions = validFilledRegions,
            processedPalette = processedPalette,
            colorInfoList = colorInfoList,
            currentColor = currentColor,
            totalProgress = totalProgress
        )
    }
    
    /**
     * 快速设置第三阶段：应用数据和恢复UI
     */
    private suspend fun setupProjectFastPhase3(data: FastProjectSetupData) = withContext(Dispatchers.Main) {
        Log.d(TAG, "Fast Phase 3: 应用数据和恢复UI")
        
        // 异步设置ColoringView数据
        binding.coloringView.setColoringDataAsync(data.coloringData, data.outlineBitmap)
        
        // 更新颜色列表（保持进度显示）
        uiStateManager.updateColorList(data.colorInfoList, preserveProgress = true)
        
        // 更新总体进度
        uiStateManager.updateTotalProgress(data.totalProgress.first, data.totalProgress.second)
        
        // 延迟恢复进度和设置当前颜色，避免阻塞UI
        binding.coloringView.post {
            // 恢复填色进度
            val restoreSuccess = binding.coloringView.restoreProgressSafely(data.validFilledRegions)
            Log.d(TAG, "进度恢复结果: $restoreSuccess")
            
            // 设置当前选中的颜色
            data.currentColor?.let { currentColor ->
                val colorIndex = data.processedPalette.indexOfFirst { 
                    normalizeColorHex(it.colorHex) == normalizeColorHex(currentColor.colorHex)
                }
                
                if (colorIndex >= 0) {
                    binding.coloringView.setCurrentColor(currentColor.colorHex)
                    uiStateManager.selectColor(currentColor, colorIndex)
                    
                    val progress = coloringStateManager.getCurrentColorProgress()
                    uiStateManager.updateCurrentColorDisplay(currentColor, progress)
                    
                    Log.d(TAG, "恢复颜色选择: ${currentColor.name} (${progress.first}/${progress.second})")
                }
            }
        }
        
        uiStateManager.showReady()
        
        // 结束端到端性能监控（快速设置版本）
        val projectId = data.coloringData.let { 
            intent.getStringExtra("project_id") ?: "unknown" 
        }
        
        PerformanceMonitor.measurePhase("EndToEnd_$projectId", "FastSetup_ShowReady_Complete") { }
        
        // 计算并记录端到端总时间
        val endToEndTime = PerformanceMonitor.endTiming("EndToEnd_ProjectClick_$projectId")
        Log.i("PERFORMANCE_E2E", "=== 端到端性能监控完成（快速设置）===")
        Log.i("PERFORMANCE_E2E", "项目ID: $projectId")
        Log.i("PERFORMANCE_E2E", "总耗时: ${endToEndTime}ms")
        Log.i("PERFORMANCE_E2E", "完成时间: ${System.currentTimeMillis()}")
        
        // 输出详细的阶段性能报告
        Log.i("PERFORMANCE_E2E", PerformanceMonitor.generateReport("EndToEnd_$projectId"))
        
        Log.d(TAG, "快速设置UI恢复完成")
    }
    
    /**
     * 快速项目设置数据类
     */
    private data class FastProjectSetupData(
        val coloringData: ColoringData,
        val outlineBitmap: Bitmap,
        val validFilledRegions: Set<Int>,
        val processedPalette: List<ColorPalette>,
        val colorInfoList: List<com.example.coloringproject.model.ColorInfo>,
        val currentColor: ColorPalette?,
        val totalProgress: Pair<Int, Int>
    )

    /**
     * 加载保存的进度
     */
    private fun loadSavedProgress(projectName: String) {
        lifecycleScope.launch {
            val progressResult = progressSaveManager.loadSavedProgress(projectName)
            when (progressResult) {
                is com.example.coloringproject.utils.LoadResult.Success -> {
                    val fullProgressData = progressResult.data
                    coloringStateManager.restoreProgress(fullProgressData.filledRegions)
                    binding.coloringView.restoreProgressSafely(fullProgressData.filledRegions)
                    Log.d(TAG, "进度恢复成功: ${fullProgressData.filledRegions.size}个区域")
                }
                is com.example.coloringproject.utils.LoadResult.Error -> {
                    Log.d(TAG, "没有保存的进度: $projectName")
                }
            }
        }
    }

    /**
     * 颜色完成后更新颜色列表
     */
    private fun updateColorListAfterCompletion() {
        // 获取更新后的颜色列表（已筛选掉完成的颜色）
        val updatedColorInfoList = coloringStateManager.getUpdatedColorInfoList()

        // 更新UI中的颜色列表
        uiStateManager.updateColorList(updatedColorInfoList, preserveProgress = true)

        Log.d(TAG, "颜色完成后更新列表: 剩余 ${updatedColorInfoList.size} 个未完成颜色")
    }

    /**
     * 切换到下一个未完成的颜色
     */
    private fun switchToNextUnfinishedColor() {
        val processedPalette = coloringStateManager.getProcessedPalette()
        val coloringData = coloringStateManager.currentColoringData ?: return

        val nextColor = processedPalette.find { palette ->
            val normalizedColorHex = normalizeColorHex(palette.colorHex)
            val colorRegions = coloringData.regions.filter { region ->
                normalizeColorHex(region.colorHex) == normalizedColorHex
            }
            val filledCount = colorRegions.count { region ->
                coloringStateManager.filledRegions.contains(region.id)
            }
            filledCount < colorRegions.size
        }

        if (nextColor != null) {
            coloringStateManager.selectColor(nextColor)
            Log.d(TAG, "自动切换到下一个颜色: ${nextColor.name}")
        }
    }

    /**
     * 处理返回按钮
     */
    private fun handleBackPressed() {
        val coloringData = coloringStateManager.currentColoringData
        if (coloringData != null && coloringStateManager.filledRegions.isNotEmpty()) {
            // 启动后台保存任务
            val previewBitmap = try {
                binding.coloringView.captureArtwork(false)
            } catch (e: Exception) {
                null
            }
            
            progressSaveManager.startBackgroundSaveTask(
                getStandardizedProjectName(),
                coloringData,
                coloringStateManager.filledRegions,
                previewBitmap,
                { binding.coloringView.captureArtwork(false) },
                currentProjectSource,
                currentResourceSource
            )
        }
        
        super.onBackPressed()
    }

    /**
     * 重置项目
     */
    private fun resetProject() {
        coloringStateManager.resetProject()
        binding.coloringView.resetColoring()
    }

    /**
     * 保存画作到相册
     */
    private fun saveArtworkToGallery() {
        if (!PermissionHelper.hasStoragePermission(this)) {
            PermissionHelper.requestStoragePermission(this)
            return
        }
        
        val artwork = binding.coloringView.captureArtwork(false)
        if (artwork != null) {
            lifecycleScope.launch {
                val success = ImageSaver.saveImageToGallery(
                    this@RefactoredSimpleMainActivity,
                    artwork,
                    "coloring_artwork_${System.currentTimeMillis()}.jpg"
                )
                ImageSaver.showSaveResult(this@RefactoredSimpleMainActivity, success)
            }
        }
    }

    /**
     * 显示项目选择菜单
     */
    private fun showProjectSelectionMenu() {
        lifecycleScope.launch {
            val projectsResult = projectLoadManager.getValidatedProjects()
            if (projectsResult.isSuccess) {
                val projects = projectsResult.getOrNull()!!
                val validProjects = projects.filter { it.isValid }
                
                if (validProjects.isNotEmpty()) {
                    uiStateManager.showProjectSelector(validProjects) { selectedProject ->
                        loadProjectByName(selectedProject.name)
                    }
                }
            }
        }
    }

    /**
     * 解析实际的项目文件名
     * 处理Gallery和Library可能传递不同格式名称的问题
     */
    private fun resolveActualProjectName(projectId: String, projectName: String?, projectSource: String?): String {
        // 简化逻辑：优先使用projectName，如果没有则使用projectId
        val candidateName = projectName ?: projectId
        Log.d(TAG, "项目名称解析: projectId=$projectId, projectName=$projectName -> $candidateName")
        return candidateName
    }
    
    /**
     * 检查项目名称是否有效（文件是否存在）
     */
    private fun isValidProjectName(projectName: String): Boolean {
        return try {
            // 检查assets中是否存在对应的JSON和PNG文件
            val jsonExists = assets.open("$projectName.json").use { true }
            val pngExists = assets.open("$projectName.png").use { true }
            jsonExists && pngExists
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 获取标准化的项目名称 - 统一使用项目ID
     */
    private fun getStandardizedProjectName(): String {
        val projectId = intent.getStringExtra("project_id") ?: "unknown"
        Log.d(TAG, "获取标准化项目名称: $projectId (统一使用项目ID)")
        return projectId
    }

    /**
     * 设置共享元素过渡 - 可选功能，为了性能可以禁用
     */
    private fun setupSharedElementTransition() {
        // 根据性能配置决定是否启用共享元素过渡
//        if (PerformanceConfig.enableSharedElementTransition()) {
//            val projectId = intent.getStringExtra("project_id") ?: intent.getStringExtra("project_name") ?: "default"
//            binding.coloringView.transitionName = "project_image_$projectId"
//
//            window.sharedElementEnterTransition = android.transition.ChangeBounds().apply { duration = 300 }
//            window.sharedElementExitTransition = android.transition.ChangeBounds().apply { duration = 300 }
//            window.enterTransition = android.transition.Fade().apply { duration = 200 }
//            window.exitTransition = android.transition.Fade().apply { duration = 200 }
//
//            Log.d(TAG, "共享元素过渡设置完成")
//        } else {
//            Log.d(TAG, "共享元素过渡已禁用以提升性能")
//        }
    }

    private fun normalizeColorHex(colorHex: String): String {
        var normalized = colorHex.trim().lowercase()
        if (!normalized.startsWith("#")) {
            normalized = "#$normalized"
        }
        if (normalized.length == 4) {
            val r = normalized[1]
            val g = normalized[2]
            val b = normalized[3]
            normalized = "#$r$r$g$g$b$b"
        }
        return normalized
    }

    // Menu相关方法
    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.main_menu, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_select_project -> {
                showProjectSelectionMenu()
                true
            }
            R.id.action_save_image -> {
                saveArtworkToGallery()
                true
            }
            R.id.action_reset -> {
                resetProject()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // 清理管理器资源
        autoDemoManager.cleanup()
        progressSaveManager.cleanup()
        Log.d(TAG, "Activity destroyed and resources cleaned up")
    }
}