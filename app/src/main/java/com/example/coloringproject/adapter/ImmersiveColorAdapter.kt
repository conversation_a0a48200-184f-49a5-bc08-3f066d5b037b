package com.example.coloringproject.adapter

import android.annotation.SuppressLint
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ProgressBar
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.coloringproject.R
import com.example.coloringproject.model.ColorInfo

/**
 * 沉浸式颜色适配器 - 支持颜色进度显示（优化版：只显示选中颜色进度）
 */
class ImmersiveColorAdapter(
    private var colors: List<ColorInfo>,
    private val onColorSelected: (ColorInfo) -> Unit
) : RecyclerView.Adapter<ImmersiveColorAdapter.ColorViewHolder>() {

    private var selectedPosition = -1
    private val colorProgress = mutableMapOf<Int, Int>() // 颜色ID -> 进度百分比
    private var showOnlySelectedProgress = true // 🚀 新增：只显示选中颜色的进度

    class ColorViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val colorView: View = itemView.findViewById(R.id.colorView)
        val progressCircle: ProgressBar = itemView.findViewById(R.id.progressCircle)
//        val selectedIndicator: ImageView = itemView.findViewById(R.id.selectedIndicator)
        val colorNumber: TextView = itemView.findViewById(R.id.colorNumber)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ColorViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_color_with_progress, parent, false)
        return ColorViewHolder(view)
    }

    override fun onBindViewHolder(holder: ColorViewHolder, @SuppressLint("RecyclerView") position: Int) {
        val colorInfo = colors[position]

        // 完全重置所有视图状态，避免复用问题
        resetViewHolderState(holder)

        // 设置颜色
        val drawable = GradientDrawable()
        drawable.shape = GradientDrawable.OVAL
        drawable.setColor(Color.parseColor(colorInfo.hexColor))
        holder.colorView.background = drawable

        // 🚀 优化：只显示选中颜色的进度
        val progress = colorProgress[colorInfo.id] ?: 0
        val isSelected = position == selectedPosition

        android.util.Log.d("ImmersiveColorAdapter", "绑定视图: ${colorInfo.name} (id=${colorInfo.id}, pos=$position) progress=$progress%, selected=$isSelected")

        // 设置进度条状态 - 只显示选中颜色的进度
        holder.progressCircle.post {
            holder.progressCircle.max = 100
            holder.progressCircle.progress = progress

            // 只显示选中颜色的进度
            holder.progressCircle.visibility = if (isSelected && progress > 0) View.VISIBLE else View.GONE

            // 强制刷新进度条drawable
            holder.progressCircle.invalidate()

            android.util.Log.d("ImmersiveColorAdapter", "进度条已更新: ${colorInfo.name} progress=$progress, visible=${holder.progressCircle.visibility == View.VISIBLE}")
        }

        // 设置选中状态
//        holder.selectedIndicator.visibility = if (isSelected) View.VISIBLE else View.GONE

        // 设置颜色编号（使用固定编号，不会因为其他颜色完成而改变）
        holder.colorNumber.text = colorInfo.fixedNumber.toString()

        // 颜色完成度100%时隐藏该颜色（从面板移除）
        if (progress >= 100) {

            holder.itemView.visibility = View.GONE
            holder.itemView.isEnabled = false
        } else {
            holder.itemView.visibility = View.VISIBLE
            holder.itemView.alpha = 1.0f
            holder.itemView.isEnabled = true
        }

        // 点击事件 - 只有未完成的颜色才能点击
        holder.itemView.setOnClickListener {
            if (progress < 100) {
                val oldPosition = selectedPosition
                selectedPosition = position
                // 更新UI
                notifyItemChanged(oldPosition)
                notifyItemChanged(selectedPosition)
                onColorSelected(colorInfo)
            }
        }
    }

    override fun getItemCount(): Int = colors.size

    /**
     * 更新颜色进度（安全版本，避免RecyclerView布局冲突）
     */
    fun updateColorProgress(colorId: Int, progress: Int) {
        val oldProgress = colorProgress[colorId] ?: -1
        colorProgress[colorId] = progress

        // 找到对应的位置并更新
        val position = colors.indexOfFirst { it.id == colorId }
        if (position != -1) {
            val colorInfo = colors[position]
            android.util.Log.d("ImmersiveColorAdapter", "更新颜色进度: ${colorInfo.name} (id=$colorId, pos=$position) $oldProgress% -> $progress%")

            // 使用Handler延迟执行，避免在RecyclerView布局计算时更新
            Handler(Looper.getMainLooper()).post {
                try {
                    notifyItemChanged(position)

                    // 如果当前颜色完成，自动切换到下一个未完成的颜色
                    if (progress >= 100) {
                        android.util.Log.d("ImmersiveColorAdapter", "颜色 ${colorInfo.name} 已完成，从面板移除")
                        if (position == selectedPosition) {
                            selectNextAvailableColor()
                        }
                    }
                } catch (e: IllegalStateException) {
                    // 如果仍然在布局计算中，再次延迟执行
                    Handler(Looper.getMainLooper()).postDelayed({
                        try {
                            notifyItemChanged(position)
                            if (progress >= 100 && position == selectedPosition) {
                                selectNextAvailableColor()
                            }
                        } catch (e: Exception) {
                            // 忽略异常，避免崩溃
                        }
                    }, 50)
                }
            }
        } else {
            android.util.Log.w("ImmersiveColorAdapter", "无法找到颜色ID $colorId 对应的位置，当前颜色数: ${colors.size}")
        }
    }

    /**
     * 选择下一个可用的颜色
     */
    private fun selectNextAvailableColor() {
        val nextPosition = colors.indexOfFirst { colorInfo ->
            (colorProgress[colorInfo.id] ?: 0) < 100
        }

        if (nextPosition != -1) {
            val oldPosition = selectedPosition
            selectedPosition = nextPosition

            notifyItemChanged(oldPosition)
            notifyItemChanged(selectedPosition)

            onColorSelected(colors[nextPosition])
        } else {
            // 所有颜色都完成了
            selectedPosition = -1
        }
    }

    /**
     * 获取当前选中的颜色
     */
    fun getSelectedColor(): ColorInfo? {
        return if (selectedPosition >= 0 && selectedPosition < colors.size) {
            colors[selectedPosition]
        } else null
    }

    /**
     * 设置选中的颜色
     */
    fun setSelectedColor(colorInfo: ColorInfo) {
        val position = colors.indexOf(colorInfo)
        if (position != -1) {
            val oldPosition = selectedPosition
            selectedPosition = position

            // 🚀 优化：如果只显示选中颜色进度，需要刷新相关项目
            if (showOnlySelectedProgress) {
                // 刷新旧的选中项（隐藏进度）
                if (oldPosition >= 0) notifyItemChanged(oldPosition)
                // 刷新新的选中项（显示进度）
                notifyItemChanged(selectedPosition)
            } else {
                notifyItemChanged(oldPosition)
                notifyItemChanged(selectedPosition)
            }
        }
    }

    /**
     * 🚀 新增：设置进度显示模式
     */
    fun setShowOnlySelectedProgress(showOnly: Boolean) {
        if (showOnlySelectedProgress != showOnly) {
            showOnlySelectedProgress = showOnly
            // 刷新所有项目以应用新的显示模式
            notifyDataSetChanged()
            android.util.Log.d("ImmersiveColorAdapter", "进度显示模式已更改: showOnlySelected=$showOnly")
        }
    }

    /**
     * 更新颜色列表
     * @param preserveProgress 是否保留现有的进度数据
     */
    fun updateColors(newColors: List<ColorInfo>, preserveProgress: Boolean = false) {
        android.util.Log.d("ImmersiveColorAdapter", "updateColors called with ${newColors.size} colors, preserveProgress=$preserveProgress")

        // 如果需要保留进度，先备份当前进度
        val backupProgress = if (preserveProgress) {
            colorProgress.toMap()
        } else {
            emptyMap()
        }

        colors = newColors
        selectedPosition = -1

        if (!preserveProgress) {
            colorProgress.clear()
            android.util.Log.d("ImmersiveColorAdapter", "Cleared all color progress")
        } else {
            // 恢复进度数据，只保留新颜色列表中存在的颜色进度
            colorProgress.clear()
            newColors.forEach { colorInfo ->
                backupProgress[colorInfo.id]?.let { progress ->
                    colorProgress[colorInfo.id] = progress
                    android.util.Log.d("ImmersiveColorAdapter", "Restored progress for color ${colorInfo.id}: $progress%")
                }
            }
        }

        notifyDataSetChanged()
    }

    /**
     * 获取总体完成进度
     */
    fun getOverallProgress(): Int {
        if (colors.isEmpty()) return 0

        val totalProgress = colors.sumOf { colorInfo ->
            colorProgress[colorInfo.id] ?: 0
        }

        return totalProgress / colors.size
    }

    /**
     * 获取所有颜色的进度状态（用于调试）
     */
    fun getProgressStatus(): Map<Int, Int> {
        return colorProgress.toMap()
    }

    /**
     * 调试输出当前进度状态
     */
    fun debugProgressStatus() {
        android.util.Log.d("ImmersiveColorAdapter", "=== 适配器进度状态 ===")
        colors.forEachIndexed { index, colorInfo ->
            val progress = colorProgress[colorInfo.id] ?: 0
            android.util.Log.d("ImmersiveColorAdapter", "颜色 ${colorInfo.id} (${colorInfo.name}): $progress%")
        }
        android.util.Log.d("ImmersiveColorAdapter", "=== 进度状态结束 ===")
    }

    /**
     * 重置ViewHolder的所有状态，避免视图复用问题
     */
    private fun resetViewHolderState(holder: ColorViewHolder) {
        // 重置进度条
        holder.progressCircle.progress = 0
        holder.progressCircle.max = 100
        holder.progressCircle.visibility = View.VISIBLE

        // 重置选中状态
//        holder.progressCircle.visibility = View.GONE

        // 重置item状态
        holder.itemView.alpha = 1.0f
        holder.itemView.isEnabled = true
        holder.itemView.visibility = View.VISIBLE

        // 清除点击监听器
        holder.itemView.setOnClickListener(null)
    }

    /**
     * 强制刷新所有视图，解决视图复用问题
     */
    fun forceRefreshAll() {
        android.util.Log.d("ImmersiveColorAdapter", "强制刷新所有视图")
        notifyDataSetChanged()
    }
}
